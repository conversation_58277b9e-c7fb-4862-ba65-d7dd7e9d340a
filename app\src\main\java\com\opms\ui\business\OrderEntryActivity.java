package com.opms.ui.business;

import android.Manifest;
import android.app.DatePickerDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.enums.BusinessImgType;
import com.opms.common.enums.OrderStatus;
import com.opms.common.utils.MultiImageManager;
import com.opms.data.model.request.OrderItemRequest;
import com.opms.data.model.request.OrderRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.CustomerResponse;
import com.opms.data.model.response.OrderResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.data.repository.OrderRepository;
import com.opms.databinding.ActivityOrderEntryBinding;
import com.opms.ui.business.adapter.OrderItemAdapter;
import com.opms.ui.business.dialog.CustomerSelectionDialog;
import com.opms.ui.business.dialog.ProductSelectionDialog;
import com.opms.ui.business.dialog.UserSelectionDialog;
import com.opms.utils.SerialNumber;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class OrderEntryActivity extends AppCompatActivity {

    private static final String TAG = "OrderEntry";

    @Inject
    OrderRepository orderRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    private ActivityOrderEntryBinding binding;
    private OrderItemAdapter orderItemAdapter;
    private MultiImageManager imageManager;
    private DecimalFormat decimalFormat = new DecimalFormat("#0.00");
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

    // 图片选择相关
    private ActivityResultLauncher<Intent> imagePickerLauncher;
    private ActivityResultLauncher<String> permissionLauncher;

    // 选中的数据
    private CustomerResponse selectedCustomer;
    private UserResponse selectedOrderUser;
    private String tempOrderId; // 临时订单ID，用于新增模式下的图片上传
    private boolean isOrderMoneyManuallyModified = false; // 标记用户是否手动修改过订单金额

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityOrderEntryBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupImagePicker();
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        setupTextWatchers();
        setupImageManager();
        initializeDefaultValues();
    }

    private void setupImagePicker() {
        // 图片选择器
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        Intent data = result.getData();
                        List<Uri> imageUris = new ArrayList<>();

                        if (data.getClipData() != null) {
                            // 多选
                            for (int i = 0; i < data.getClipData().getItemCount(); i++) {
                                Uri imageUri = data.getClipData().getItemAt(i).getUri();
                                imageUris.add(imageUri);
                            }
                        } else if (data.getData() != null) {
                            // 单选
                            imageUris.add(data.getData());
                        }

                        if (!imageUris.isEmpty() && imageManager != null) {
                            imageManager.addImages(imageUris);
                        }
                    }
                }
        );

        // 权限请求
        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                isGranted -> {
                    if (isGranted) {
                        openImagePicker();
                    } else {
                        showError("需要存储权限才能选择图片");
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("订单录入");
        }
    }

    private void setupRecyclerView() {
        orderItemAdapter = new OrderItemAdapter();
        orderItemAdapter.setOnOrderItemChangeListener(new OrderItemAdapter.OnOrderItemChangeListener() {
            @Override
            public void onQuantityChanged() {
                updateTotalAmount();
            }

            @Override
            public void onItemRemoved() {
                updateTotalAmount();
            }
        });

        binding.rvOrderItems.setLayoutManager(new LinearLayoutManager(this));
        binding.rvOrderItems.setAdapter(orderItemAdapter);
    }

    private void setupClickListeners() {
        // 客户选择
        binding.etCustomer.setOnClickListener(v -> showCustomerSelectionDialog());

        // 下单日期选择
        binding.etOrderDate.setOnClickListener(v -> showDatePicker(binding.etOrderDate, "选择下单日期"));

        // 接单人员选择
        binding.etOrderUser.setOnClickListener(v -> showUserSelectionDialog());

        // 要求完成日期选择
        binding.etCompletionDate.setOnClickListener(v -> showDatePicker(binding.etCompletionDate, "选择要求完成日期"));

        // 添加产品
        binding.btnAddProduct.setOnClickListener(v -> showProductSelectionDialog());

        // 保存按钮
        binding.btnSave.setOnClickListener(v -> saveOrder());

        // 取消按钮
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void setupTextWatchers() {
        // 监听订单金额的手动修改
        binding.etOrderMoney.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 标记用户手动修改了订单金额
                if (binding.etOrderMoney.hasFocus()) {
                    isOrderMoneyManuallyModified = true;
                }
            }
        });
    }

    private void setupImageManager() {
        Log.d(TAG, "setupImageManager: 设置多图片管理器");

        // 生成临时订单ID用于新增模式
        tempOrderId = "temp_order_" + System.currentTimeMillis();

        // 初始化多图片管理器
        imageManager = new MultiImageManager(this, binding.rvOrderImages);

        // 设置配置 - 新增模式下使用临时ID
        imageManager.setup(
                imageUploadRepository,
                BusinessImgType.ORDER,
                tempOrderId,
                getCurrentUser(),
                false // 新增模式
        );

        // 设置图片操作监听器
        imageManager.setOnImageActionListener(new MultiImageManager.OnImageActionListener() {
            @Override
            public void onAddImageClick() {
                Log.d(TAG, "点击添加图片按钮");
                checkPermissionAndOpenImagePicker();
            }

            @Override
            public void onImageClick(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "点击图片: " + position);
                // 可以添加图片预览功能
            }

            @Override
            public void onImageLongClick(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "长按图片: " + position);
                // 可以添加图片选项菜单
            }

            @Override
            public void onImageDelete(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "删除图片: " + position);
                imageManager.deleteImage(item, position);
            }

            @Override
            public void onImageUploadProgress(int uploadedCount, int totalCount) {
                Log.d(TAG, "上传进度: " + uploadedCount + "/" + totalCount);
            }

            @Override
            public void onImageUploadComplete(List<String> successUrls, List<String> failureMessages) {
                Log.d(TAG, "上传完成，成功: " + successUrls.size() + ", 失败: " + failureMessages.size());
                if (!failureMessages.isEmpty()) {
                    showError("部分图片上传失败");
                } else if (!successUrls.isEmpty()) {
                    showError("图片上传成功");
                }
            }
        });

        Log.d(TAG, "setupImageManager: 多图片管理器设置完成");
    }

    private void initializeDefaultValues() {
        // 设置默认下单日期为今天
        String today = dateFormat.format(new Date());
        binding.etOrderDate.setText(today);

        // 初始化金额为0
        binding.etTotalMoney.setText("0.00");
        binding.etOrderMoney.setText("0.00");
        binding.etReceivedMoney.setText("0.00");
    }

    private void showCustomerSelectionDialog() {
        CustomerSelectionDialog dialog = CustomerSelectionDialog.newInstance();
        dialog.setOnCustomerSelectedListener(customer -> {
            selectedCustomer = customer;
            binding.etCustomer.setText(customer.getName() + " (" + customer.getCode() + ")");
        });
        dialog.show(getSupportFragmentManager(), "CustomerSelectionDialog");
    }

    private void showUserSelectionDialog() {
        UserSelectionDialog dialog = UserSelectionDialog.newInstance();
        dialog.setOnUserSelectedListener(user -> {
            selectedOrderUser = user;
            binding.etOrderUser.setText(user.getName() + " (" + user.getUsername() + ")");
        });
        dialog.show(getSupportFragmentManager(), "UserSelectionDialog");
    }

    private void showProductSelectionDialog() {
        ProductSelectionDialog dialog = ProductSelectionDialog.newInstance();
        dialog.setOnProductSelectedListener(product -> {
            orderItemAdapter.addOrderItem(product);
            updateTotalAmount();
        });
        dialog.show(getSupportFragmentManager(), "ProductSelectionDialog");
    }

    private void showDatePicker(com.google.android.material.textfield.TextInputEditText editText, String title) {
        Calendar calendar = Calendar.getInstance();

        // 如果输入框已有日期，则使用该日期作为初始值
        String currentText = editText.getText().toString().trim();
        if (!TextUtils.isEmpty(currentText)) {
            try {
                Date date = dateFormat.parse(currentText);
                if (date != null) {
                    calendar.setTime(date);
                }
            } catch (Exception e) {
                // 使用当前日期
            }
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(year, month, dayOfMonth);
                    String selectedDate = dateFormat.format(calendar.getTime());
                    editText.setText(selectedDate);
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        datePickerDialog.setTitle(title);
        datePickerDialog.show();
    }

    private void updateTotalAmount() {
        double totalAmount = orderItemAdapter.getTotalAmount();
        binding.etTotalMoney.setText(decimalFormat.format(totalAmount));

        // 如果用户没有手动修改过订单金额，则自动同步订单总额到订单金额
        if (!isOrderMoneyManuallyModified) {
            binding.etOrderMoney.setText(decimalFormat.format(totalAmount));
        }
    }

    private void saveOrder() {
        if (!validateInput()) {
            return;
        }

        OrderRequest request = createOrderRequest();
        showLoading(true);

        Call<ApiResponse<OrderResponse>> call = orderRepository.createOrder(request);
        call.enqueue(new Callback<ApiResponse<OrderResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<OrderResponse>> call,
                                   @NonNull Response<ApiResponse<OrderResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<OrderResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        OrderResponse newOrder = apiResponse.getData();
                        if (newOrder != null && newOrder.getId() > 0) {
                            // 更新图片的业务ID关联
                            if (imageManager != null) {
                                Log.d(TAG, "订单创建成功，ID: " + newOrder.getId() + "，更新图片业务ID关联");
                                imageManager.updateBusinessId(String.valueOf(newOrder.getId()));
                            }
                        }

                        Snackbar.make(binding.getRoot(), "订单录入成功", Snackbar.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "订单录入失败");
                    }
                } else {
                    showError("订单录入失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<OrderResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "订单录入失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        // 客户验证
        if (selectedCustomer == null) {
            showError("请选择客户");
            return false;
        }

        // 下单日期验证
        String orderDate = binding.etOrderDate.getText().toString().trim();
        if (TextUtils.isEmpty(orderDate)) {
            showError("请选择下单日期");
            return false;
        }

        // 接单人员验证
        if (selectedOrderUser == null) {
            showError("请选择接单人员");
            return false;
        }

        // 要求完成日期验证
        String completionDate = binding.etCompletionDate.getText().toString().trim();
        if (TextUtils.isEmpty(completionDate)) {
            showError("请选择要求完成日期");
            return false;
        }

        // 产品列表验证
        if (orderItemAdapter.getOrderItems().isEmpty()) {
            showError("请至少添加一个产品");
            return false;
        }

        return true;
    }

    private OrderRequest createOrderRequest() {
        OrderRequest request = new OrderRequest();
        String code = SerialNumber.createSerialNumber("ODD");
        // 基本信息
        request.setCode(code);
        request.setCustomerCode(selectedCustomer.getCode());
        request.setOrderDate(binding.etOrderDate.getText().toString().trim());
        request.setOrderUser(selectedOrderUser.getUsername());
        request.setCompletionDate(binding.etCompletionDate.getText().toString().trim());
        request.setStatus(OrderStatus.PENDING.getCode()); // 默认状态为未处理
        request.setRemark(binding.etRemark.getText().toString().trim());

        // 金额信息
        try {
            double totalMoney = Double.parseDouble(binding.etTotalMoney.getText().toString().trim());
            double orderMoney = Double.parseDouble(binding.etOrderMoney.getText().toString().trim());
            double receivedMoney = Double.parseDouble(binding.etReceivedMoney.getText().toString().trim());

            request.setTotalMoney(totalMoney);
            request.setOrderMoney(orderMoney);
            request.setReceivedMoney(receivedMoney);
        } catch (NumberFormatException e) {
            request.setTotalMoney(0.0);
            request.setOrderMoney(0.0);
            request.setReceivedMoney(0.0);
        }

        // 订单项列表
        List<OrderItemRequest> items = new ArrayList<>();
        for (OrderItemAdapter.OrderItem orderItem : orderItemAdapter.getOrderItems()) {
            OrderItemRequest itemRequest = new OrderItemRequest();
            itemRequest.setParentCode(code);
            itemRequest.setProductId(orderItem.getProduct().getId());
            itemRequest.setNumber(orderItem.getQuantity());
            itemRequest.setStatus(OrderStatus.PENDING.getCode()); // 默认状态为未处理
            items.add(itemRequest);
        }
        request.setItems(items);

        return request;
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnSave.setEnabled(!show);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private String getCurrentUser() {
        // 这里应该从SharedPreferences或其他地方获取当前用户信息
        // 暂时返回一个默认值
        return "current_user";
    }

    private void checkPermissionAndOpenImagePicker() {
        Log.d(TAG, "checkPermissionAndOpenImagePicker: 检查权限并打开图片选择器");

        // 检查权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用READ_MEDIA_IMAGES权限
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_MEDIA_IMAGES权限");
                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES);
                return;
            }
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE权限
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_EXTERNAL_STORAGE权限");
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE);
                return;
            }
        }

        openImagePicker();
    }

    private void openImagePicker() {
        Log.d(TAG, "openImagePicker: 启动多图片选择器");
        try {
            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
            intent.setType("image/*");
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
            intent.addCategory(Intent.CATEGORY_OPENABLE);
            imagePickerLauncher.launch(intent);
        } catch (Exception e) {
            Log.e(TAG, "启动图片选择器失败: " + e.getMessage(), e);
            showError("启动图片选择器失败");
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
