<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_customer_selection" modulePackage="com.opms" filePath="app\src\main\res\layout\item_customer_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_customer_selection_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="87" endOffset="51"/></Target><Target id="@+id/tv_customer_name" view="TextView"><Expressions/><location startLine="19" startOffset="8" endLine="25" endOffset="31"/></Target><Target id="@+id/tv_contact" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="37"/></Target><Target id="@+id/tv_customer_code" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="52" endOffset="38"/></Target><Target id="@+id/tv_company_name" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="71" endOffset="35"/></Target><Target id="@+id/tv_contact_phone" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="81" endOffset="45"/></Target></Targets></Layout>