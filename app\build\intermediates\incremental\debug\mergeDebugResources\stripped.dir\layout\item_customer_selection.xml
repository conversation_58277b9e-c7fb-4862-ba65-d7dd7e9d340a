<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左侧列：客户名称、联系人、公司名称 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingEnd="8dp">

            <TextView
                android:id="@+id/tv_customer_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="客户名称" />

            <TextView
                android:id="@+id/tv_contact"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                tools:text="联系人：张三" />

            <TextView
                android:id="@+id/tv_company_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                tools:text="公司名称" />

        </LinearLayout>

        <!-- 右侧列：客户编号、联系人电话 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="8dp">

            <TextView
                android:id="@+id/tv_customer_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                tools:text="编号：C001" />

            <TextView
                android:id="@+id/tv_contact_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                tools:text="电话：13800138000" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
