<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 第一行：客户名称（跨两列） -->
        <TextView
            android:id="@+id/tv_customer_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="客户名称" />

        <!-- 第二行：左侧联系人，右侧客户编号 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_contact"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingEnd="8dp"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                tools:text="联系人：张三" />

            <TextView
                android:id="@+id/tv_customer_code"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingStart="8dp"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                tools:text="编号：C001" />

        </LinearLayout>

        <!-- 第三行：左侧公司名称，右侧联系人电话 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_company_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingEnd="8dp"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                tools:text="公司名称" />

            <TextView
                android:id="@+id/tv_contact_phone"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingStart="8dp"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                tools:text="电话：13800138000" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
