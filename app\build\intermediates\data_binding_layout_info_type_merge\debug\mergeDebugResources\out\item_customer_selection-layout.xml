<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_customer_selection" modulePackage="com.opms" filePath="app\src\main\res\layout\item_customer_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_customer_selection_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="83" endOffset="51"/></Target><Target id="@+id/tv_customer_name" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="35"/></Target><Target id="@+id/tv_contact" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="41" endOffset="37"/></Target><Target id="@+id/tv_company_name" view="TextView"><Expressions/><location startLine="43" startOffset="12" endLine="50" endOffset="35"/></Target><Target id="@+id/tv_customer_code" view="TextView"><Expressions/><location startLine="62" startOffset="12" endLine="68" endOffset="38"/></Target><Target id="@+id/tv_contact_phone" view="TextView"><Expressions/><location startLine="70" startOffset="12" endLine="77" endOffset="45"/></Target></Targets></Layout>