// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCustomerSelectionBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView tvCompanyName;

  @NonNull
  public final TextView tvContact;

  @NonNull
  public final TextView tvContactPhone;

  @NonNull
  public final TextView tvCustomerCode;

  @NonNull
  public final TextView tvCustomerName;

  private ItemCustomerSelectionBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView tvCompanyName, @NonNull TextView tvContact,
      @NonNull TextView tvContactPhone, @NonNull TextView tvCustomerCode,
      @NonNull TextView tvCustomerName) {
    this.rootView = rootView;
    this.tvCompanyName = tvCompanyName;
    this.tvContact = tvContact;
    this.tvContactPhone = tvContactPhone;
    this.tvCustomerCode = tvCustomerCode;
    this.tvCustomerName = tvCustomerName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCustomerSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCustomerSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_customer_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCustomerSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_company_name;
      TextView tvCompanyName = ViewBindings.findChildViewById(rootView, id);
      if (tvCompanyName == null) {
        break missingId;
      }

      id = R.id.tv_contact;
      TextView tvContact = ViewBindings.findChildViewById(rootView, id);
      if (tvContact == null) {
        break missingId;
      }

      id = R.id.tv_contact_phone;
      TextView tvContactPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvContactPhone == null) {
        break missingId;
      }

      id = R.id.tv_customer_code;
      TextView tvCustomerCode = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerCode == null) {
        break missingId;
      }

      id = R.id.tv_customer_name;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      return new ItemCustomerSelectionBinding((MaterialCardView) rootView, tvCompanyName, tvContact,
          tvContactPhone, tvCustomerCode, tvCustomerName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
